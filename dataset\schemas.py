import pandera as pa
from pandera import Column, Check, DataFrameSchema

# Schema for match prediction training data
match_schema = DataFrameSchema({
    # Basic identifiers and target
    "match_id": Column(pa.Int, Check.gt(0), nullable=False, coerce=True),
    "radiant_win": Column(pa.Bool, nullable=False, coerce=True),

    # Hero IDs for 10 players
    **{
        f"{team}_player_{i}_hero_id": Column(
            pa.Int, Check.in_range(1, 300), nullable=False, coerce=True
        )
        for team in ("radiant", "dire") for i in range(1, 6)
    },

    # Example combat stats (kills, deaths, assists)
    **{
        f"{team}_player_{i}_{stat}": Column(
            pa.Int, Check.ge(0), nullable=False, coerce=True
        )
        for team in ("radiant", "dire")
        for i in range(1, 6)
        for stat in ("kills", "deaths", "assists")
    },

    # Economy stats
    **{
        f"{team}_player_{i}_{stat}": Column(
            pa.Float, Check.ge(0), nullable=False, coerce=True
        )
        for team in ("radiant", "dire")
        for i in range(1, 6)
        for stat in ("gold_per_min", "xp_per_min", "net_worth")
    },

    # Farming stats
    **{
        f"{team}_player_{i}_{stat}": Column(
            pa.Int, Check.ge(0), nullable=False, coerce=True
        )
        for team in ("radiant", "dire")
        for i in range(1, 6)
        for stat in ("last_hits", "denies")
    },

    # Support stats
    **{
        f"{team}_player_{i}_{stat}": Column(
            pa.Float, Check.ge(0), nullable=True, coerce=True
        )
        for team in ("radiant", "dire")
        for i in range(1, 6)
        for stat in ("obs_placed", "sen_placed")
    },

    # Teamfight participation
    **{
        f"{team}_player_{i}_teamfight_participation": Column(
            pa.Float, nullable=True, coerce=True
        )
        for team in ("radiant", "dire")
        for i in range(1, 6)
    },

    # Performance stats
    **{
        f"{team}_player_{i}_{stat}": Column(
            pa.Float, Check.ge(0), nullable=False, coerce=True
        )
        for team in ("radiant", "dire")
        for i in range(1, 6)
        for stat in ("hero_damage", "tower_damage")
    },

    # Meta stats
    **{
        f"{team}_player_{i}_{stat}": Column(
            pa.Float, Check.ge(0), nullable=True, coerce=True
        )
        for team in ("radiant", "dire")
        for i in range(1, 6)
        for stat in ("level", "roshans_killed")
    },

    # Player-hero winrate features
    **{
        f"{team}_player_{i}_player_hero_winrate": Column(
            pa.Float, Check.in_range(0.0, 1.0), nullable=False, coerce=True
        )
        for team in ("radiant", "dire")
        for i in range(1, 6)
    },
})
