#!/usr/bin/env python3
"""
Training Setup Validation Script
Validates that the training environment is properly configured for GPU-accelerated XGBoost training
"""

import sys
import os
import logging
from pathlib import Path
import importlib

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_environment():
    """Check if we're in the correct Mamba environment"""
    print("🔍 Checking environment setup...")
    
    # Check if we're in a conda/mamba environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env != 'dota2predictor-env':
        print(f"⚠️  Not in dota2predictor-env (current: {conda_env})")
        print("   Run: mamba activate dota2predictor-env")
        return False
    
    print(f"✅ Environment: {conda_env}")
    return True

def check_gpu_availability():
    """Check GPU and CUDA availability"""
    print("\n🎮 Checking GPU setup...")
    
    try:
        import subprocess
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected")
            # Extract GPU info
            lines = result.stdout.split('\n')
            for line in lines:
                if 'RTX' in line or 'GTX' in line:
                    print(f"   GPU: {line.strip()}")
                    break
        else:
            print("❌ nvidia-smi failed")
            return False
    except FileNotFoundError:
        print("❌ nvidia-smi not found")
        return False
    
    return True

def check_xgboost_gpu():
    """Test XGBoost GPU functionality"""
    print("\n🚀 Testing XGBoost GPU support...")
    
    try:
        import xgboost as xgb
        import numpy as np
        
        print(f"✅ XGBoost version: {xgb.__version__}")
        
        # Test GPU training
        X = np.random.rand(100, 10)
        y = np.random.randint(0, 2, 100)
        
        from xgboost import XGBClassifier
        
        # Test GPU parameters
        model = XGBClassifier(
            device='cuda',
            tree_method='hist',
            n_estimators=10,
            random_state=42
        )
        
        model.fit(X, y)
        predictions = model.predict(X)
        
        print("✅ XGBoost GPU training successful")
        return True
        
    except Exception as e:
        print(f"❌ XGBoost GPU test failed: {e}")
        return False

def check_model_files():
    """Check if model files exist and are accessible"""
    print("\n📁 Checking model files...")
    
    model_files = [
        'xgb_model.pkl',
        'xgb_model_hero_pick.pkl', 
        'xgb_model_dota_plus.pkl',
        'scaler.pkl',
        'scaler_dota_plus.pkl'
    ]
    
    all_exist = True
    for model_file in model_files:
        if Path(model_file).exists():
            print(f"✅ {model_file}")
        else:
            print(f"⚠️  {model_file} (missing - will be created during training)")
            all_exist = False
    
    return all_exist

def check_training_data():
    """Check if training data exists"""
    print("\n📊 Checking training data...")

    # Check processed training data
    processed_data = 'dataset/train_data/all_data_match_predict.csv'
    if Path(processed_data).exists():
        print(f"✅ {processed_data}")
        processed_exists = True
    else:
        print(f"⚠️  {processed_data} (will be created during training)")
        processed_exists = False

    # Check Kaggle dataset structure
    kaggle_base = Path('dataset/kaggle_data')
    if not kaggle_base.exists():
        print(f"❌ {kaggle_base} (missing)")
        print("\n📝 To set up Kaggle data:")
        print("1. Download: https://www.kaggle.com/datasets/devinanzelmo/dota-2-pro-league-matches-2016-2025")
        print("2. Extract to dataset/kaggle_data/ directory")
        print("3. Run: python dataset/create_training_data_from_kaggle.py")
        return False

    print(f"✅ {kaggle_base}")

    # Check for year/month directories
    year_dirs = []
    month_dirs = []

    for item in kaggle_base.iterdir():
        if item.is_dir():
            dir_name = item.name
            if dir_name.isdigit() and len(dir_name) == 4:  # Year directories (2020, 2021, etc.)
                year_dirs.append(dir_name)
            elif dir_name.isdigit() and len(dir_name) == 6:  # Month directories (202501, etc.)
                month_dirs.append(dir_name)

    year_dirs.sort()
    month_dirs.sort()

    print(f"   Year directories: {year_dirs}")
    print(f"   Month directories: {month_dirs}")

    # Check for required files in at least one directory
    sample_dirs = year_dirs[-1:] + month_dirs[-1:]  # Check latest year and month
    required_files = ['main_metadata.csv', 'players.csv', 'picks_bans.csv']

    files_found = False
    for sample_dir in sample_dirs:
        sample_path = kaggle_base / sample_dir
        if all((sample_path / file).exists() for file in required_files):
            print(f"✅ Required files found in {sample_dir}")
            files_found = True
            break

    if not files_found:
        print("❌ Required CSV files not found in dataset directories")
        print(f"   Looking for: {required_files}")
        return False

    return True

def check_database_connection():
    """Check database connectivity"""
    print("\n🗄️  Checking database connection...")
    
    try:
        from db.database_operations import get_database_session
        
        with get_database_session() as session:
            # Simple query to test connection
            result = session.execute("SELECT 1").fetchone()
            if result:
                print("✅ Database connection successful")
                return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("   Check PostgreSQL service and config.py settings")
        return False
    
    return False

def check_ml_imports():
    """Check if ML modules can be imported"""
    print("\n🧠 Checking ML module imports...")
    
    modules = [
        ('ml.model', 'MainML'),
        ('structure.helpers', 'prepare_match_prediction_data'),
        ('dataset.schemas', 'training data schemas'),
        ('db.database_operations', 'database functions')
    ]
    
    all_imported = True
    for module_name, description in modules:
        try:
            importlib.import_module(module_name)
            print(f"✅ {module_name} ({description})")
        except ImportError as e:
            print(f"❌ {module_name} failed: {e}")
            all_imported = False
    
    return all_imported

def test_training_pipeline():
    """Test the complete training pipeline"""
    print("\n🔄 Testing training pipeline...")
    
    try:
        from ml.model import MainML
        import pandas as pd
        import numpy as np
        
        # Create dummy data for testing
        dummy_data = pd.DataFrame({
            'feature_1': np.random.rand(100),
            'feature_2': np.random.rand(100),
            'feature_3': np.random.rand(100),
            'radiant_win': np.random.randint(0, 2, 100)
        })
        
        # Test MainML initialization
        main_ml = MainML(dummy_data, 'test_model.pkl')
        print("✅ MainML initialization successful")
        
        # Clean up test file
        if Path('test_model.pkl').exists():
            Path('test_model.pkl').unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ Training pipeline test failed: {e}")
        return False

def main():
    """Main validation function"""
    print("🎯 Dota 2 Predictor - Training Setup Validation")
    print("=" * 60)
    
    checks = [
        ("Environment Setup", check_environment),
        ("GPU Availability", check_gpu_availability), 
        ("XGBoost GPU Support", check_xgboost_gpu),
        ("Model Files", check_model_files),
        ("Training Data", check_training_data),
        ("Database Connection", check_database_connection),
        ("ML Module Imports", check_ml_imports),
        ("Training Pipeline", test_training_pipeline)
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        result = check_func()
        results.append((check_name, result))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} checks passed")
    
    if passed == len(results):
        print("\n🎉 All checks passed! Training environment is ready.")
        print("\n📝 Next steps:")
        print("1. Run: python dataset/create_training_data_from_kaggle.py")
        print("2. Run: python ml/create_model_match_predict.py")
        print("3. Start bot: python start.py")
    else:
        print("\n⚠️  Some checks failed. Please address the issues above.")
        print("\n🔧 Common fixes:")
        print("- Activate environment: mamba activate dota2predictor-env")
        print("- Install GPU drivers and CUDA toolkit")
        print("- Set up PostgreSQL database")
        print("- Download Kaggle dataset")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
