import pandas as pd
import numpy as np
import os
import sys

# Add the project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandera as pa
from dataset.schemas import match_schema

def validate_dataset_structure(df):
    """Validate the basic structure of the dataset"""
    print("=== DATASET STRUCTURE VALIDATION ===")
    print(f"Dataset shape: {df.shape}")
    print(f"Total features: {len(df.columns) - 1}")  # -1 for target column
    print(f"Total samples: {len(df)}")
    
    # Check if we have the expected number of features
    # This has been updated for the new feature set
    expected_columns = 370 
    
    if len(df.columns) == expected_columns:
        print(f"✓ Column count matches expected: {expected_columns}")
    else:
        print(f"✗ Column count mismatch. Expected: {expected_columns}, Got: {len(df.columns)}")
    
    # Check for required columns
    required_columns = ['match_id', 'radiant_win']
    missing_required = [col for col in required_columns if col not in df.columns]
    if not missing_required:
        print("✓ All required columns present")
    else:
        print(f"✗ Missing required columns: {missing_required}")
    
    return True

def validate_player_features(df):
    """Validate that all player features are present"""
    print("\n=== PLAYER FEATURES VALIDATION ===")
    
    expected_features = [
        'hero_id', 'kills', 'deaths', 'assists', 'gold_per_min', 'xp_per_min',
        'teamfight_participation', 'obs_placed', 'sen_placed', 'net_worth',
        'roshans_killed', 'last_hits', 'denies', 'level', 'hero_damage',
        'tower_damage', 'player_hero_winrate'
    ]
    
    missing_features = []
    
    for team in ['radiant', 'dire']:
        for player_num in range(1, 6):
            for feature in expected_features:
                column_name = f"{team}_player_{player_num}_{feature}"
                if column_name not in df.columns:
                    missing_features.append(column_name)
    
    if not missing_features:
        print("✓ All player features present for both teams")
    else:
        print(f"✗ Missing player features: {missing_features[:10]}...")  # Show first 10
        
    # Check for player-hero win rate feature specifically
    winrate_features = [col for col in df.columns if 'player_hero_winrate' in col]
    if len(winrate_features) >= 10: # Check for at least 10, since we have team-level aggregates too
        print("✓ Player-hero win rate features present for all 10 players")
    else:
        print(f"✗ Expected at least 10 player-hero win rate features, found {len(winrate_features)}")
    
    return len(missing_features) == 0

def validate_data_quality(df):
    """Validate data quality and check for anomalies"""
    print("\n=== DATA QUALITY VALIDATION ===")
    
    # Check for missing values
    missing_counts = df.isnull().sum()
    columns_with_missing = missing_counts[missing_counts > 0]
    
    if len(columns_with_missing) == 0:
        print("✓ No missing values found")
    else:
        print(f"✗ Missing values found in {len(columns_with_missing)} columns:")
        for col, count in columns_with_missing.head(10).items():
            print(f"  - {col}: {count} missing values")
    
    # Check target variable distribution
    target_counts = df['radiant_win'].value_counts()
    radiant_win_pct = target_counts[True] / len(df) * 100
    dire_win_pct = target_counts[False] / len(df) * 100
    
    print(f"\nTarget variable distribution:")
    print(f"  Radiant wins: {target_counts[True]} ({radiant_win_pct:.1f}%)")
    print(f"  Dire wins: {target_counts[False]} ({dire_win_pct:.1f}%)")
    
    # Check if distribution is reasonable (between 40-60% for each side)
    if 40 <= radiant_win_pct <= 60:
        print("✓ Target distribution is balanced")
    else:
        print("⚠ Target distribution might be imbalanced")
    
    # Check hero ID ranges (should be valid Dota 2 hero IDs)
    hero_columns = [col for col in df.columns if 'hero_id' in col]
    hero_ids = pd.concat([df[col] for col in hero_columns]).dropna()
    
    print(f"\nHero ID validation:")
    print(f"  Unique heroes: {hero_ids.nunique()}")
    print(f"  Hero ID range: {hero_ids.min()} - {hero_ids.max()}")
    
    # Check for negative values in features that shouldn't be negative
    non_negative_features = ['kills', 'deaths', 'assists', 'gold_per_min', 'xp_per_min',
                           'net_worth', 'last_hits', 'denies', 'level', 'hero_damage']
    
    negative_issues = []
    for feature in non_negative_features:
        feature_columns = [col for col in df.columns if col.endswith(f'_{feature}')]
        for col in feature_columns:
            if (df[col] < 0).any():
                negative_issues.append(col)
    
    if not negative_issues:
        print("✓ No negative values in features that should be non-negative")
    else:
        print(f"✗ Found negative values in: {negative_issues[:5]}...")
    
    return len(columns_with_missing) == 0 and len(negative_issues) == 0

def validate_player_hero_winrates(df):
    """Validate the player-hero win rate feature"""
    print("\n=== PLAYER-HERO WIN RATE VALIDATION ===")
    
    winrate_columns = [col for col in df.columns if 'player_hero_winrate' in col and 'skewness' not in col and 'confidence' not in col]
    
    for col in winrate_columns:
        winrates = df[col].dropna()
        
        # Check if all values are between 0 and 1
        if not ((winrates >= 0) & (winrates <= 1)).all():
            print(f"✗ {col} has values outside [0,1] range")
            continue
        
        # Check for reasonable distribution
        mean_winrate = winrates.mean()
        std_winrate = winrates.std()
        
        if col == winrate_columns[0]:  # Print stats for first column as example
            print(f"Win rate statistics (example: {col}):")
            print(f"  Mean: {mean_winrate:.3f}")
            print(f"  Std: {std_winrate:.3f}")
            print(f"  Min: {winrates.min():.3f}")
            print(f"  Max: {winrates.max():.3f}")
    
    # Check if default values (0.5) are reasonable
    default_values = sum((df[col] == 0.5).sum() for col in winrate_columns)
    total_values = len(df) * len(winrate_columns)
    default_pct = default_values / total_values * 100
    
    print(f"\nDefault win rate values (0.5): {default_values}/{total_values} ({default_pct:.1f}%)")
    
    if default_pct < 50:
        print("✓ Most players have calculated win rates (not defaults)")
    else:
        print("⚠ High percentage of default win rates - might need more data")
    
    return True

def validate_match_consistency(df):
    """Validate match-level consistency"""
    print("\n=== MATCH CONSISTENCY VALIDATION ===")
    
    # Check for duplicate match IDs
    duplicate_matches = df['match_id'].duplicated().sum()
    if duplicate_matches == 0:
        print("✓ No duplicate match IDs")
    else:
        print(f"✗ Found {duplicate_matches} duplicate match IDs")
    
    # Check that each match has exactly 10 unique heroes
    sample_matches = df.sample(min(100, len(df))).copy()  # Check sample for performance
    
    hero_consistency_issues = 0
    for _, row in sample_matches.iterrows():
        heroes = []
        for team in ['radiant', 'dire']:
            for player_num in range(1, 6):
                hero_id = row[f"{team}_player_{player_num}_hero_id"]
                if pd.notna(hero_id):
                    heroes.append(hero_id)
        
        if len(heroes) != 10:
            hero_consistency_issues += 1
        elif len(set(heroes)) != 10:  # Check for duplicate heroes
            hero_consistency_issues += 1
    
    if hero_consistency_issues == 0:
        print("✓ Match hero consistency validated (sample check)")
    else:
        print(f"✗ Found {hero_consistency_issues} matches with hero issues in sample")
    
    return duplicate_matches == 0 and hero_consistency_issues == 0

def generate_feature_summary(df):
    """Generate a summary of all features"""
    print("\n=== FEATURE SUMMARY ===")
    
    # Group features by type
    feature_types = {
        'hero_id': [],
        'combat': ['kills', 'deaths', 'assists'],
        'economy': ['gold_per_min', 'xp_per_min', 'net_worth'],
        'farming': ['last_hits', 'denies'],
        'support': ['obs_placed', 'sen_placed'],
        'performance': ['hero_damage', 'tower_damage', 'teamfight_participation'],
        'meta': ['level', 'roshans_killed'],
        'ml_features': ['player_hero_winrate']
    }
    
    for feature_type, features in feature_types.items():
        if features:
            type_columns = []
            for feature in features:
                type_columns.extend([col for col in df.columns if col.endswith(f'_{feature}')])
            
            if type_columns:
                sample_col = type_columns[0]
                sample_data = df[sample_col].dropna()
                print(f"{feature_type.upper()} features ({len(type_columns)} columns):")
                print(f"  Example: {sample_col}")
                print(f"  Range: {sample_data.min():.2f} - {sample_data.max():.2f}")
                print(f"  Mean: {sample_data.mean():.2f}")
    
    return True

def main():
    """Main validation function"""
    print("DOTA 2 TRAINING DATA VALIDATION")
    print("=" * 50)
    
    # Load the dataset
    data_path = "dataset/train_data/all_data_match_predict.csv"
    if not os.path.exists(data_path):
        print(f"✗ Dataset not found at {data_path}")
        return False
    
    try:
        df = pd.read_csv(data_path)
        print(f"✓ Dataset loaded successfully from {data_path}")
        try:
            match_schema.validate(df, lazy=True)
            print("✓ Schema validation passed")
        except Exception as e:
            print("✗ Schema validation failed:")
            print(e)
            return False
    except Exception as e:
        print(f"✗ Error loading dataset: {e}")
        return False
    
    # Run all validation checks
    validations = [
        validate_dataset_structure(df),
        validate_player_features(df),
        validate_data_quality(df),
        validate_player_hero_winrates(df),
        validate_match_consistency(df),
        generate_feature_summary(df)
    ]
    
    # Summary
    print("\n" + "=" * 50)
    print("VALIDATION SUMMARY")
    print("=" * 50)
    
    if all(validations):
        print("✓ All validations passed! Data is ready for training.")
        print(f"✓ Dataset contains {len(df)} matches with {len(df.columns)-1} features each.")
        print("✓ Player-hero win rates successfully integrated.")
        print("✓ Data quality checks passed.")
        return True
    else:
        print("✗ Some validations failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
