# Pull Request Title

## Description

Please include a summary of the changes and the related issue number. Provide relevant motivation and context. If this fixes a bug or resolves a feature, include a link to the related issue.

- **Issue Number**: [Issue #]
- **Description of changes**: Explain the code, algorithms, or models modified/added.

## Type of Change

Please delete options that are not relevant:

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Code refactor
- [ ] Performance improvement

## How Has This Been Tested?

Please describe the tests that you ran to verify your changes. Provide instructions so we can reproduce. Include any relevant details for configuration.

- **Unit Tests Added**: [Yes/No]
  - If yes, describe the tests and if they were successful.
- **Model Accuracy Improvement**: [Yes/No]
  - If yes, provide before and after metrics of the prediction model accuracy (e.g., accuracy, precision, recall, etc.)

**Test Configuration**:
- Python version: [e.g., 3.9.19]
- ML Framework/Library: [e.g., XGBoost]
- Dataset: [e.g., all_data.csv]

## Checklist

- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published in downstream modules

## Additional Context or Screenshots (if applicable)

Include any additional context or relevant screenshots related to the prediction models or new features. If the change impacts the Dota 2 match prediction outcomes, provide graphs or results showing the impact.
