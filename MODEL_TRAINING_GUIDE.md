# Dota 2 Predictor - Model Training and Utilization Guide

## Overview

The Dota 2 Predictor system uses XGBoost models to predict match outcomes and analyze hero picks. The system supports three different models:

1. **Match Prediction Model** (`xgb_model.pkl`) - Predicts which team will win a match
2. **Hero Pick Analysis Model** (`xgb_model_hero_pick.pkl`) - Analyzes the strength of team compositions
3. **Dota Plus Model** (`xgb_model_dota_plus.pkl`) - Tracks win probability during live matches

## Prerequisites

### Environment Setup
- **Mamba Environment**: Use `dota2predictor-env` with GPU support (see `MAMBA_SETUP_README.md`)
- **GPU Requirements**: NVIDIA RTX 4070 with CUDA 12.4 for optimal training performance
- **Python Version**: 3.11 (specified in `environment.yml`)
- **XGBoost**: 2.1+ with GPU acceleration enabled

### Required Services
- PostgreSQL database configured (see `config.py`)
- OpenDota API key (optional but recommended for better rate limits)
- Steam API key for live match data

### Environment Activation
**Always activate the Mamba environment before training:**
```bash
mamba activate dota2predictor-env
python verify_gpu.py  # Verify GPU functionality
```

## Model Training Methods

### 1. Kaggle Dataset Training (Recommended - New)

**The fastest and most reliable method for training with complete professional match data.**

This method uses the comprehensive Kaggle dataset which contains detailed match data from professional tournaments, eliminating the need for OpenDota API calls during training.

#### Prerequisites
- Download the Kaggle dataset: [Dota 2 Pro League Matches 2016-2025](https://www.kaggle.com/datasets/devinanzelmo/dota-2-pro-league-matches-2016-2025)
- Extract the complete dataset to `dataset/kaggle_data/` directory
- **Dataset Structure**: The data is organized by year and month:
  - **Annual folders**: `2020/`, `2021/`, `2022/`, `2023/`, `2024/` (combined yearly data)
  - **Monthly folders**: `202501/`, `202502/`, `202503/`, `202504/`, `202505/`, `202506/`, `202507/` (2025 monthly data)
  - **Constants folder**: `Constants/` (hero/item definitions)

#### Required Files (in each year/month folder):
- `main_metadata.csv` - Match outcomes and metadata
- `players.csv` - Player statistics for each match
- `picks_bans.csv` - Hero picks and bans for each match
- `teams.csv` - Team information
- `objectives.csv` - Game objectives (Roshan, towers, etc.)
- `draft_timings.csv` - Pick/ban timing data
- Additional files: `chat.csv`, `cosmetics.csv`, `teamfights.csv`, etc.

#### 0. Data Sanity Check (Recommended)

Before training, it's crucial to ensure the raw Kaggle/OpenDota exports conform to the expected schema. This step validates the data structure, types, and completeness using Pandera.

```bash
# Activate environment and verify GPU
mamba activate dota2predictor-env
python verify_gpu.py

# Validate training data
python dataset/validate_training_data.py
```

**What this does:**
- Validates the structure and data types of `dataset/train_data/all_data_match_predict.csv` against the schema defined in `dataset/schemas.py`.
- Catches missing values, incorrect data types, and other data quality issues early.
- If this script fails, it indicates a problem with the generated dataset that needs to be addressed before training.

**Adjusting the Schema:**
If new features are added or data types change, you may need to update the schema in `dataset/schemas.py`. This file defines the expected structure and constraints for the training data.

#### Step 1: Generate Enhanced Training Dataset

```bash
# Ensure environment is activated
mamba activate dota2predictor-env

# Generate training dataset with GPU acceleration
python dataset/create_training_data_from_kaggle.py
```

**What this does:**
- **Processes multiple years of professional match data** from the Kaggle dataset:
  - **2020-2024**: Complete yearly datasets (thousands of matches per year)
  - **2025**: Monthly datasets (202501-202507) with latest tournament data
  - **Automatic data combination**: Script intelligently combines all available data
- **Calculates player-hero win rates** directly from the comprehensive dataset
- **Generates 370+ features per match**, including:
  - **170 individual player features** (kills, GPM, XPM, etc.)
  - **198 advanced team-level statistical features** (std, min, max, skewness, etc. for 13 key metrics)
  - **Player-hero win rate features** calculated from thousands of professional matches
  - **Tournament-specific features** from major competitions
- **Output**: `dataset/train_data/all_data_match_predict.csv` with comprehensive feature set

**Data Selection**: By default, processes data from 2023 onwards. To include more historical data:
```python
# Edit the script to change start_year parameter
load_kaggle_data(base_dir="dataset/kaggle_data", start_year=2020)  # Include all years
```

#### Step 2: Train the Model

```bash
# Ensure environment is activated and GPU is available
mamba activate dota2predictor-env
python verify_gpu.py

# Train model with GPU acceleration
python ml/create_model_match_predict.py
```

**What this does:**
- Loads the enhanced dataset with player-hero win rates
- Applies feature engineering and scaling
- Trains XGBoost model with **GPU acceleration** using the full 370-feature set
- Optimizes for RTX 4070 (12GB VRAM) with CUDA 12.4
- Saves `xgb_model.pkl` and `scaler.pkl`
- Evaluates model performance with GPU metrics

#### Advantages of Kaggle Dataset Method
- ✅ **No API rate limits** - all data is local
- ✅ **Much faster** - no network delays
- ✅ **More reliable** - no API downtime issues
- ✅ **Enhanced features** - includes player-hero win rates
- ✅ **Comprehensive data** - 967+ professional matches
- ✅ **Validated pipeline** - thorough data quality checks
- ✅ **GPU acceleration** - optimized for RTX 4070 training

#### GPU Performance Optimization
- **RTX 4070 (12GB VRAM)**: Optimal batch sizes 1000-5000 samples
- **CUDA 12.4**: Latest toolkit for maximum compatibility
- **Training Speed**: 10-50x faster than CPU-only training
- **Memory Management**: Automatic VRAM optimization
- **Monitoring**: Use `nvidia-smi` to monitor GPU utilization during training

#### Dataset Features Generated
The enhanced Kaggle pipeline creates **370+ features** per match from **5+ years of professional data**, broken down as follows:

**Data Volume:**
- **2020-2024**: Complete yearly datasets with thousands of professional matches
- **2025**: Monthly updates (202501-202507) with latest tournament results
- **Total matches**: 10,000+ professional matches across major tournaments
- **Player-hero combinations**: Hundreds of thousands of data points for win rate calculations

**Feature Breakdown:**
- **170 Individual Player Features**: 17 features for each of the 10 players, including kills, deaths, GPM, XPM, net worth, and **player-hero winrate calculated from extensive historical data**
- **198 Advanced Team-Level Statistical Features**: For each team, we calculate **standard deviation, min, max, range, skewness, and confidence scores** for 13 key performance indicators (GPM, XPM, KDA, etc.). This provides deep insights into team consistency and skill variance
- **8 Simple Team-Level Aggregations**: Sum and average for support-oriented stats like wards placed and roshans killed
- **Tournament Context Features**: Derived from multiple years of professional competition data
- **2-3 Core Match Identifiers/Targets**: `match_id` and `radiant_win`

**Enhanced Player-Hero Win Rates:**
- Calculated from **5+ years of professional match data**
- **Statistical significance**: Minimum game thresholds for reliable win rates
- **Temporal weighting**: Recent performance weighted more heavily
- **Professional context**: Only tournament-level matches included

### 2. Automatic Incremental Training

The system automatically learns from new data as predictions are made and match results become available.

**How it works:**
- Every prediction is stored in the PostgreSQL database with match features
- The system periodically fetches actual match results from OpenDota API
- When enough new results are available (batch size defined in `config.py`), the model automatically retrains
- This happens every time the bot receives a message

**Configuration:**
- Set `incremental_learning_batch` in `config.py` (default: 50 matches)
- The system tracks the last trained row ID to avoid reprocessing data

**No manual intervention required** - just use the bot normally and it will learn automatically.

### 3. Full Manual Retraining (Legacy Method)

For complete model overhaul with new datasets or when starting fresh. **Note: The Kaggle dataset method (Method 1) is now preferred over this approach.**

#### Step 1: Generate New Training Data

```bash
python dataset/generate_dataset_match_predict.py
```

**What this does:**
- Fetches match data from major tournaments listed in the script
- Creates separate CSV files for each tournament (e.g., `The_International_2024.csv`)
- **Warning:** This process takes several hours and makes thousands of API calls

#### Step 2: Combine Tournament Data

The training script expects a single combined dataset:

1. Manually merge all `premium_league_matches_*.csv` files created in Step 1
2. Save the combined file as `all_data_match_predict.csv`
3. Place it in `dataset/train_data/` directory (overwrite existing file)

**Example Python script to combine files:**
```python
import pandas as pd
import glob

# Read all CSV files matching the pattern
csv_files = glob.glob("premium_league_matches_*.csv")
combined_df = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)
combined_df.to_csv("dataset/train_data/all_data_match_predict.csv", index=False)
```

#### Step 3: Train New Model

```bash
python ml/create_model_match_predict.py
```

**What this does:**
- Loads the combined dataset
- Applies feature engineering (team-level aggregations)
- Trains new XGBoost model
- Saves `xgb_model.pkl` and `scaler.pkl` to project root
- Evaluates model performance

#### Similar Process for Other Models

**Hero Pick Model:**
```bash
python dataset/generate_dataset_hero_pick.py
python ml/create_model_hero_pick.py
```

**Dota Plus Model:**
```bash
python ml/create_model_dota_plus.py
```

## Kaggle Dataset Pipeline Files

### Kaggle Dataset Support Files

- `dataset/create_training_data_from_kaggle.py` - Main data processing script (handles all years/months)
- `dataset/validate_training_data.py` - Comprehensive data validation
- `dataset/kaggle_data/` - Complete Kaggle dataset directory structure:
  - **Annual folders**: `2020/`, `2021/`, `2022/`, `2023/`, `2024/` (complete yearly data)
  - **Monthly folders**: `202501/` through `202507/` (2025 monthly updates)
  - **Constants folder**: `Constants/` (hero definitions, item data, etc.)

### Data Pipeline Architecture

The Kaggle dataset pipeline follows this flow:

1. **Multi-Year Data Loading**:
   - **Automatically discovers** all year/month directories in `dataset/kaggle_data/`
   - **Loads and combines** main_metadata.csv, players.csv, picks_bans.csv from each directory
   - **Filters by year**: Configurable start year (default: 2023) for training data selection
   - **Handles both formats**: Annual folders (2020-2024) and monthly folders (202501-202507)

2. **Enhanced Win Rate Calculation**:
   - **Merge across years**: Combines picks with players across all available data
   - **Calculate win outcomes** based on team affiliation and radiant_win results
   - **Statistical significance**: Groups by player-hero combinations with minimum game thresholds
   - **Temporal weighting**: Recent performance weighted more heavily than historical data
   - **Professional context**: Only tournament-level matches included for accuracy

3. **Advanced Feature Engineering**:
   - **370+ features per match**: 10 players × 17+ features each plus team aggregations
   - **Historical context**: Player-hero win rates calculated from years of professional data
   - **Tournament features**: Derived from major competition results across multiple years
   - **Consistent ordering**: Maintains team_slot ordering for model compatibility

4. **Comprehensive Quality Assurance**:
   - **Multi-year validation**: Ensures data consistency across different time periods
   - **Schema validation**: Validates structure, completeness, and data types
   - **Statistical validation**: Checks for outliers and data quality issues

5. **GPU-Optimized Training**: Feeds validated multi-year dataset to XGBoost with GPU acceleration

### Player-Hero Win Rate Feature

This new feature significantly improves prediction accuracy by incorporating:
- **Individual player expertise** with specific heroes
- **Historical performance data** calculated from the dataset
- **Balanced defaults** (0.5 win rate for new player-hero combinations)
- **Statistical significance** (minimum game requirements)

**Example values:**
- Professional player with 10+ games on a hero: 0.65 win rate
- New player-hero combination: 0.5 default win rate
- Player struggling with specific hero: 0.30 win rate

This feature helps the model understand that not all players are equally skilled with all heroes, which is crucial for accurate match prediction.

## Model Features

### Match Prediction Features (Team-level aggregations)

For both Radiant and Dire teams:
- `avg_hero_winrate` - Average professional win rate of selected heroes
- `avg_roshans_killed` - Average Roshan kills per player
- `avg_last_hits` - Average last hits per player
- `avg_denies` - Average denies per player
- `avg_hero_damage` - Average hero damage per player
- `avg_gpm` - Average gold per minute
- `avg_xpm` - Average experience per minute
- `avg_net_worth` - Average net worth
- `avg_player_level` - Average player level
- `sum_obs` - Total observer wards placed
- `sum_sen` - Total sentry wards placed
- `avg_teamfight_participation_cols` - Average teamfight participation
- `avg_kda` - Average KDA ratio

### Hero Pick Features

- Hero counter-pick win rates against opposing team
- Professional hero win rates
- Team composition synergy metrics

## Live Prediction Enhancements

The live prediction pipeline has been significantly upgraded to improve accuracy for professional tournament matches.

### 1. Professional Data Source (`lobby_type=2`)
- The system now correctly fetches player history from **tournament matches** (`lobby_type=2`) instead of practice matches.
- Includes a fallback to ranked matches (`lobby_type=7`) if no recent tournament data is available for a player.

### 2. Player-Hero Expertise Features
- For live predictions, the system now fetches **player-specific hero statistics** from the OpenDota API.
- This adds three critical new features for each player in a live match:
  - `hero_games_played`: Total games the player has on that hero.
  - `hero_winrate`: The player's personal win rate with that hero.
  - `last_played`: A timestamp indicating how recently the player used that hero.
- These features allow the model to weigh player experience and mastery heavily in its predictions.

## Utilizing the Models

### Through Telegram Bot

The primary interface is the Telegram bot (`start.py`):

1. **Start the bot:**
   ```bash
   # Activate environment first
   mamba activate dota2predictor-env

   # Verify GPU functionality (optional)
   python verify_gpu.py

   # Start the bot
   python start.py
   ```

2. **Available commands:**
   - Predict match results for live tournament games
   - Analyze hero pick strength
   - Track live match win probability (Dota Plus feature)
   - View prediction history and accuracy

### Programmatic Usage

```python
from ml.model import MainML

# Load the match prediction model
main_ml = MainML(None, "xgb_model.pkl")
main_ml.load_model()

# Make prediction on new data (DataFrame with proper features)
prediction, probabilities = main_ml.predict(new_data_df)

# prediction[0] = 1 (Radiant wins) or 0 (Dire wins)
# probabilities[0] = [dire_prob, radiant_prob]
```

### Data Preparation for Predictions

Use the helper functions to prepare data:

```python
from structure.helpers import prepare_match_prediction_data

# For match prediction
df = prepare_match_prediction_data(raw_df, "scaler.pkl")

# For hero pick analysis
from structure.helpers import prepare_hero_pick_data
df = prepare_hero_pick_data(raw_df)
```

## Model Performance Monitoring

### Automatic Tracking

- The system automatically tracks prediction accuracy
- Win rate is displayed on the bot's main screen
- All predictions and results are stored in PostgreSQL database

### Manual Analysis

```python
from db.database_operations import calculate_win_rate, get_history_data_as_dataframe

# Get current win rate
win_rate, total_predictions = calculate_win_rate()
print(f"Win Rate: {win_rate:.2%} ({total_predictions} predictions)")

# Get all historical data for analysis
df = get_history_data_as_dataframe()
```

## File Structure

```
dota2predictor/
├── ml/
│   ├── model.py                     # Main ML class with training/prediction logic
│   ├── create_model_match_predict.py   # Match prediction model training
│   ├── create_model_hero_pick.py       # Hero pick model training
│   └── create_model_dota_plus.py       # Dota Plus model training
├── dataset/
│   ├── generate_dataset_match_predict.py  # Data collection script (legacy)
│   ├── create_training_data_from_kaggle.py  # Kaggle dataset processor
│   ├── validate_training_data.py           # Data validation script
│   ├── kaggle_data/                        # Kaggle dataset directory
│   │   ├── 2022/                          # 2022 professional matches
│   │   │   ├── main_metadata.csv
│   │   │   ├── players.csv
│   │   │   ├── picks_bans.csv
│   │   │   ├── teams.csv
│   │   │   ├── objectives.csv
│   │   │   └── [other CSV files]s
│   │   ├── 2023/                          # 2023 professional matches
│   │   ├── 2024/                          # 2024 professional matches
│   │   ├── 202501/                        # January 2025 matches
│   │   ├── 202502/                        # February 2025 matches
│   │   ├── 202503/                        # March 2025 matches
│   │   ├── 202504/                        # April 2025 matches
│   │   ├── 202505/                        # May 2025 matches
│   │   ├── 202506/                        # June 2025 matches
│   │   ├── 202507/                        # July 2025 matches
│   │   └── Constants/                     # Hero/item definitions
│   └── train_data/
│       └── all_data_match_predict.csv     # Enhanced training dataset
├── structure/
│   ├── struct.py                    # Data structures and API interactions
│   └── helpers.py                   # Feature engineering functions
├── db/
│   ├── database_operations.py       # Database operations
│   └── setup.py                     # Database schema
├── xgb_model.pkl                    # Trained match prediction model
├── xgb_model_hero_pick.pkl          # Trained hero pick model
├── xgb_model_dota_plus.pkl          # Trained Dota Plus model
├── scaler.pkl                       # Feature scaler for match prediction
└── start.py                         # Telegram bot entry point
```

## Troubleshooting

### Common Issues

1. **GPU/CUDA Issues:**
   - **GPU not detected**: Run `nvidia-smi` to verify driver installation
   - **CUDA version mismatch**: Ensure CUDA 12.4 compatibility with `mamba list cuda`
   - **Out of memory**: Reduce batch size or use CPU fallback
   - **XGBoost GPU errors**: Verify GPU-enabled XGBoost installation

2. **Environment Issues:**
   - **Mamba not found**: Use Miniforge Prompt, not regular Command Prompt
   - **Environment not activated**: Always run `mamba activate dota2predictor-env` first
   - **Package conflicts**: Recreate environment with `mamba env create -f environment.yml`

3. **API Rate Limits:**
   - OpenDota API has rate limits (especially without API key)
   - Add delays between requests or use API key

4. **Missing Dependencies:**
   - Ensure Mamba environment is properly set up (see `MAMBA_SETUP_README.md`)
   - Check PostgreSQL connection settings
   - Verify all conda-forge packages are installed

5. **Data Quality:**
   - Some matches may have incomplete data
   - The system filters out invalid matches automatically
   - Run `python dataset/validate_training_data.py` to check data integrity

6. **Model Performance:**
   - Low accuracy may indicate need for more training data
   - Consider feature engineering improvements
   - Check for data drift in recent matches
   - Monitor GPU utilization during training with `nvidia-smi`

### Debugging

Enable detailed logging by modifying `logging.conf` or adding:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Optimization

### For Large Datasets

1. **Incremental Training:** Use the built-in incremental learning instead of full retraining
2. **Batch Processing:** Increase batch size for incremental training
3. **Feature Selection:** Remove less important features to reduce model complexity
4. **Parallel Processing:** Consider parallel data collection for dataset generation

### Model Improvements

1. **Feature Engineering:**
   - Add player-hero experience features
   - Include team synergy metrics
   - Add tournament-specific features

2. **Ensemble Methods:**
   - Combine XGBoost with neural networks
   - Use multiple models for different game phases

3. **Hyperparameter Tuning:**
   - Use cross-validation for optimal XGBoost parameters
   - Consider Bayesian optimization for hyperparameter search

## API Documentation

### Key Classes

- `MainML`: Core machine learning class for training and prediction
- `Match`: Represents a Dota 2 match with all player and team data
- `Player`: Individual player with statistics and hero information
- `Hero`: Hero-specific data including win rates and matchups
- `Tournament`: Collection of matches from a specific tournament

### Configuration

Edit `config.py` to modify:
- Database connection settings
- API keys
- Incremental learning batch size
- Logging configuration

This guide provides comprehensive instructions for both automated and manual model training, as well as various ways to utilize the trained models for Dota 2 match prediction.
