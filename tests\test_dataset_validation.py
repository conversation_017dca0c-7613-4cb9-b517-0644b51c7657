import pandas as pd
import pytest
from dataset.schemas import match_schema

@pytest.mark.parametrize("csv_path", [
    "dataset/train_data/all_data_match_predict.csv",
])
def test_match_schema(csv_path):
    """
    Load a sample of the match prediction CSV and validate against the Pandera schema.
    Test will fail if the schema validation raises any errors.
    """
    # Load a sample to keep test lightweight
    df_sample = pd.read_csv(csv_path, nrows=10000)
    # Should not raise if schema is satisfied
    match_schema.validate(df_sample, lazy=True)
