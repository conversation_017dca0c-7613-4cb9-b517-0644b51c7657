# AI Agent Prompt for Dota 2 Model Training

## 🎯 **Primary Training Prompt**

```
You are working on the Dota 2 Match Result Predictor project. I need you to train the machine learning model following the updated MODEL_TRAINING_GUIDE.md with the correct Kaggle dataset structure.

CRITICAL CONTEXT:
- We are using a Mamba/Miniforge environment with GPU acceleration (RTX 4070, CUDA 12.4)
- The project uses XGBoost with GPU optimization for training
- All system guidance is available in .clinerules/ directory for reference
- The training process has been updated to use GPU acceleration

KAGGLE DATASET STRUCTURE:
- Location: dataset/kaggle_data/
- Annual folders: 2020/, 2021/, 2022/, 2023/, 2024/ (complete yearly data)
- Monthly folders: 202501/, 202502/, 202503/, 202504/, 202505/, 202506/, 202507/ (2025 monthly data)
- Constants folder: Constants/ (hero/item definitions)
- Each folder contains: main_metadata.csv, players.csv, picks_bans.csv, teams.csv, objectives.csv, etc.

BEFORE STARTING:
1. Review the system guidance in .clinerules/dota2predictor_system_guidance.md
2. Check the current MODEL_TRAINING_GUIDE.md for the complete process
3. Ensure you understand the GPU-optimized training workflow
4. Validate the Kaggle dataset structure is correct

TRAINING REQUIREMENTS:
- Use Method 1 (Kaggle Dataset) as recommended in the guide
- Follow the exact command sequence with Mamba environment activation
- Verify GPU functionality before training
- Process multi-year dataset (2020-2025) for comprehensive training data
- Monitor GPU utilization during training
- Handle any GPU-related errors with appropriate fallbacks

VALIDATION STEPS:
1. Run the validation script: python validate_training_setup.py
2. Verify GPU setup: python verify_gpu.py
3. Check Kaggle dataset structure in dataset/kaggle_data/
4. Follow the step-by-step training process in MODEL_TRAINING_GUIDE.md

EXPECTED WORKFLOW:
1. Activate environment: mamba activate dota2predictor-env
2. Validate setup and GPU functionality
3. Verify Kaggle dataset structure (5+ years of data)
4. Process comprehensive dataset with create_training_data_from_kaggle.py
5. Train model with GPU acceleration using 370+ features
6. Verify model performance and GPU utilization

DATA EXPECTATIONS:
- 10,000+ professional matches from 2020-2025
- 370+ features per match including player-hero win rates
- GPU-accelerated training should be 10-50x faster than CPU
- Training data will be saved to dataset/train_data/all_data_match_predict.csv

Please proceed with the training process, following the updated guide exactly, and report any issues or successful completion with performance metrics including GPU utilization.
```

## 🔧 **Dataset Structure Validation Prompt**

```
Before starting training, validate the Kaggle dataset structure:

EXPECTED STRUCTURE:
dataset/kaggle_data/
├── 2020/ (complete 2020 professional matches)
├── 2021/ (complete 2021 professional matches)
├── 2022/ (complete 2022 professional matches)
├── 2023/ (complete 2023 professional matches)
├── 2024/ (complete 2024 professional matches)
├── 202501/ (January 2025 matches)
├── 202502/ (February 2025 matches)
├── 202503/ (March 2025 matches)
├── 202504/ (April 2025 matches)
├── 202505/ (May 2025 matches)
├── 202506/ (June 2025 matches)
├── 202507/ (July 2025 matches)
└── Constants/ (hero/item definitions)

REQUIRED FILES IN EACH DIRECTORY:
- main_metadata.csv (match outcomes and metadata)
- players.csv (player statistics for each match)
- picks_bans.csv (hero picks and bans)
- teams.csv (team information)
- objectives.csv (game objectives)
- draft_timings.csv (pick/ban timing)

VALIDATION COMMANDS:
1. python validate_training_setup.py
2. Check dataset structure manually if needed
3. Verify file sizes are reasonable (players.csv should be largest)

If dataset structure is incorrect, provide specific guidance on fixing it.
```

## 🚀 **Performance Monitoring Prompt**

```
During training, monitor and report:

GPU UTILIZATION:
- Use nvidia-smi to monitor GPU usage (should be >80% during training)
- Training should be 10-50x faster than CPU-only training
- Watch for CUDA out-of-memory errors (reduce batch size if needed)
- Verify XGBoost is using GPU with device='cuda' parameter

DATA PROCESSING:
- Script should process multiple years of data automatically
- Expected to find 10,000+ professional matches
- Player-hero win rates calculated from comprehensive dataset
- 370+ features generated per match

TRAINING METRICS:
- Report training time with GPU acceleration
- Monitor model accuracy and validation scores
- Check for overfitting with early stopping
- Verify model files are saved correctly (xgb_model.pkl, scaler.pkl)

EXPECTED OUTPUTS:
- dataset/train_data/all_data_match_predict.csv (comprehensive training data)
- xgb_model.pkl (trained XGBoost model with GPU optimization)
- scaler.pkl (feature scaler)
- Training logs with GPU utilization and performance metrics
```

## 🛠️ **Troubleshooting Prompt**

```
If you encounter issues during training:

ENVIRONMENT ISSUES:
1. Check .clinerules/quick_reference.md for debugging steps
2. Review .clinerules/technical_reference.md for implementation patterns
3. Use the troubleshooting section in MODEL_TRAINING_GUIDE.md
4. Run validate_training_setup.py to diagnose environment issues

DATASET ISSUES:
- Verify Kaggle dataset is extracted to correct location
- Check that both annual (2020-2024) and monthly (202501-202507) folders exist
- Ensure required CSV files exist in each directory
- Validate file sizes are reasonable (players.csv typically largest)

GPU ISSUES:
- Check GPU status with nvidia-smi if training fails
- Verify CUDA 12.4 compatibility
- Check XGBoost GPU installation
- Use CPU fallback if GPU training fails

DATA PROCESSING ISSUES:
- Check create_training_data_from_kaggle.py logs for errors
- Verify start_year parameter (default: 2023)
- Ensure sufficient disk space for processing
- Monitor memory usage during data processing

Report specific error messages and system state for targeted troubleshooting.
```

## 📊 **Success Validation Prompt**

```
After training completion, validate success:

MODEL VALIDATION:
1. Run python verify_gpu.py to test the trained model
2. Check model file sizes and timestamps
3. Verify prediction accuracy on test data
4. Test bot functionality with new model

PERFORMANCE VALIDATION:
- Confirm GPU acceleration was used during training
- Report training time improvement over CPU
- Validate model performance metrics
- Check feature importance and model interpretability

INTEGRATION VALIDATION:
- Test model loading in MainML class
- Verify scaler compatibility
- Test prediction pipeline end-to-end
- Confirm bot can use new model for predictions

SUCCESS CRITERIA:
✅ GPU-accelerated training completed successfully
✅ Model accuracy meets or exceeds previous versions
✅ Training time significantly reduced with GPU
✅ Model files saved correctly and load properly
✅ Bot functionality verified with new model
✅ Comprehensive dataset (5+ years) processed successfully

Report final metrics, training time, and any performance improvements achieved.
```

This comprehensive prompt set ensures the AI agent understands the correct dataset structure, follows the GPU-optimized training process, and validates success properly with the multi-year Kaggle dataset.
