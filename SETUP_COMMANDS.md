# Dota 2 Predictor - Mamba Setup Commands

## 🚀 Quick Setup Commands

### Prerequisites Check
```bash
# Check if Miniforge is installed
mamba --version

# Check NVIDIA driver
nvidia-smi
```

### Environment Creation
```bash
# Navigate to project directory
cd c:\Users\<USER>\Documents\GitHub\dota2predictor2

# Create environment (automated)
python setup_mamba_environment.py

# OR create manually
mamba env create -f environment.yml
```

### Environment Activation
```bash
# Activate environment
mamba activate dota2predictor-env

# Verify installation
python verify_gpu.py
```

## 📋 Step-by-Step Instructions

### 1. Install Miniforge (if not already installed)
1. Download: https://github.com/conda-forge/miniforge#miniforge3
2. Run `Miniforge3-Windows-x86_64.exe`
3. Choose "Install for me only"
4. **DO NOT** add to PATH

### 2. Open Miniforge Prompt
- Search "Miniforge Prompt" in Start Menu
- Navigate to project: `cd c:\Users\<USER>\Documents\GitHub\dota2predictor2`

### 3. Create Environment
```bash
# Option A: Automated setup (recommended)
python setup_mamba_environment.py

# Option B: Manual setup
mamba env create -f environment.yml
mamba activate dota2predictor-env
python verify_gpu.py
```

### 4. Daily Usage
```bash
# Always start with activation
mamba activate dota2predictor-env

# Start development
jupyter lab

# Run the bot
python start.py
```

## 🔧 Troubleshooting Commands

### Environment Issues
```bash
# Remove and recreate environment
mamba env remove -n dota2predictor-env
mamba env create -f environment.yml

# Update environment
mamba env update -f environment.yml --prune

# Clean cache
mamba clean --all
```

### GPU Issues
```bash
# Check CUDA installation
mamba list cuda

# Reinstall CUDA toolkit
mamba install cuda-toolkit=12.4 -c conda-forge

# Reinstall XGBoost
mamba uninstall xgboost
mamba install xgboost -c conda-forge
```

### Package Management
```bash
# List packages
mamba list

# Update all packages
mamba update --all

# Install new package
mamba install package_name

# Install pip package
pip install package_name
```

## ✅ Verification Checklist

- [ ] Miniforge installed and accessible
- [ ] NVIDIA driver installed (nvidia-smi works)
- [ ] Environment created successfully
- [ ] Environment activated
- [ ] GPU verification passed
- [ ] All packages imported correctly

## 🎯 Expected Output

### Successful GPU Setup
```
🎯 Dota 2 Predictor - GPU Environment Verification
==================================================
📦 Checking package installations...
✅ numpy 1.26.4: NumPy for numerical computing
✅ pandas 2.2.2: Pandas for data manipulation
✅ sklearn 1.5.1: Scikit-learn for ML utilities
✅ xgboost 2.1.0: XGBoost for gradient boosting
✅ requests 2.32.3: HTTP requests library
✅ sqlalchemy 2.0.31: Database ORM

🔍 Checking CUDA devices...
✅ NVIDIA GPU detected:
   GPU: NVIDIA GeForce RTX
   Driver Version: 560.70
   CUDA Version: 12.6

✅ XGBoost version: 2.1.0

🚀 Testing GPU training...
✅ SUCCESS! XGBoost GPU training completed
📊 Dummy model accuracy: 52%
🎮 Your RTX 4070 is ready for Dota 2 match prediction!

==================================================
🎉 Environment setup complete! Ready for GPU-accelerated ML training.
```

## 🔗 Key Files

- `environment.yml` - Mamba environment specification
- `verify_gpu.py` - GPU functionality test
- `setup_mamba_environment.py` - Automated setup script
- `MAMBA_SETUP_README.md` - Comprehensive guide

## 📞 Next Steps After Setup

1. **Start Development**: `jupyter lab`
2. **Train Models**: `python ml/create_model_match_predict.py`
3. **Run Bot**: `python start.py`
4. **Run Tests**: `python -m pytest tests/`

Your environment is now ready for robust ML development with GPU acceleration! 🚀
