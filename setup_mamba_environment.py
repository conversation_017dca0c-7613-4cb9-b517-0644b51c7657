#!/usr/bin/env python3
"""
Mamba Environment Setup Script for Dota 2 Predictor
Automates the complete setup process with GPU support verification
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def run_command(command, description, check=True, shell=True):
    """Run a command with proper error handling"""
    print(f"🔧 {description}...")
    try:
        if shell:
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True)
        else:
            result = subprocess.run(command, check=check, 
                                  capture_output=True, text=True)
        
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(f"   Error details: {e.stderr.strip()}")
        return None

def check_mamba_installation():
    """Check if Mamba is installed and accessible"""
    print("🔍 Checking Mamba installation...")
    result = run_command("mamba --version", "Checking Mamba version", check=False)
    
    if result and result.returncode == 0:
        print("✅ Mamba is installed and accessible")
        return True
    else:
        print("❌ Mamba not found. Please install Miniforge first.")
        print("📝 Installation instructions:")
        print("1. Download Miniforge3-Windows-x86_64.exe from:")
        print("   https://github.com/conda-forge/miniforge#miniforge3")
        print("2. Run the installer (choose 'Install for me only')")
        print("3. Open 'Miniforge Prompt' from Start Menu")
        print("4. Navigate to your project directory and run this script again")
        return False

def check_nvidia_driver():
    """Check NVIDIA driver installation"""
    print("🔍 Checking NVIDIA driver...")
    result = run_command("nvidia-smi", "Checking NVIDIA driver", check=False)
    
    if result and result.returncode == 0:
        print("✅ NVIDIA driver is installed")
        # Extract GPU info
        lines = result.stdout.split('\n')
        for line in lines:
            if 'RTX' in line or 'GTX' in line:
                print(f"   GPU: {line.strip()}")
        return True
    else:
        print("⚠️  NVIDIA driver not found or not working")
        print("📝 Please install the latest NVIDIA driver from:")
        print("   https://www.nvidia.com/download/index.aspx")
        return False

def create_environment():
    """Create the Mamba environment from environment.yml"""
    print("🚀 Creating Mamba environment...")
    
    # Check if environment.yml exists
    if not Path("environment.yml").exists():
        print("❌ environment.yml not found in current directory")
        return False
    
    # Remove existing environment if it exists
    print("🧹 Removing existing environment (if any)...")
    run_command("mamba env remove -n dota2predictor-env", 
                "Removing existing environment", check=False)
    
    # Create new environment
    result = run_command("mamba env create -f environment.yml", 
                        "Creating environment from environment.yml")
    
    if result and result.returncode == 0:
        print("✅ Environment created successfully")
        return True
    else:
        print("❌ Failed to create environment")
        return False

def test_environment():
    """Test the created environment"""
    print("🧪 Testing environment...")
    
    # Test basic imports
    test_script = '''
import sys
print(f"Python version: {sys.version}")

try:
    import numpy as np
    print(f"✅ NumPy {np.__version__}")
except ImportError as e:
    print(f"❌ NumPy import failed: {e}")

try:
    import pandas as pd
    print(f"✅ Pandas {pd.__version__}")
except ImportError as e:
    print(f"❌ Pandas import failed: {e}")

try:
    import xgboost as xgb
    print(f"✅ XGBoost {xgb.__version__}")
except ImportError as e:
    print(f"❌ XGBoost import failed: {e}")

try:
    import sklearn
    print(f"✅ Scikit-learn {sklearn.__version__}")
except ImportError as e:
    print(f"❌ Scikit-learn import failed: {e}")
'''
    
    # Write test script to temporary file
    with open("temp_test.py", "w") as f:
        f.write(test_script)
    
    # Run test in the environment
    result = run_command("mamba run -n dota2predictor-env python temp_test.py", 
                        "Testing basic imports")
    
    # Clean up
    if Path("temp_test.py").exists():
        Path("temp_test.py").unlink()
    
    return result and result.returncode == 0

def run_gpu_verification():
    """Run the GPU verification script"""
    print("🎮 Running GPU verification...")
    
    if not Path("verify_gpu.py").exists():
        print("❌ verify_gpu.py not found")
        return False
    
    result = run_command("mamba run -n dota2predictor-env python verify_gpu.py", 
                        "Running GPU verification")
    
    return result and result.returncode == 0

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 60)
    print("🎉 Mamba Environment Setup Complete!")
    print("=" * 60)
    print("\n📝 Next Steps:")
    print("1. Activate your environment:")
    print("   mamba activate dota2predictor-env")
    print("\n2. Verify GPU functionality:")
    print("   python verify_gpu.py")
    print("\n3. Start development:")
    print("   jupyter lab")
    print("\n4. Run the bot:")
    print("   python start.py")
    print("\n💡 Daily workflow:")
    print("   - Always start with: mamba activate dota2predictor-env")
    print("   - Update packages: mamba update --all")
    print("   - Add new packages: mamba install package_name")
    print("\n🔧 Troubleshooting:")
    print("   - Check environment: mamba list")
    print("   - Environment info: mamba info")
    print("   - Clean cache: mamba clean --all")

def main():
    """Main setup function"""
    print("🎯 Dota 2 Predictor - Mamba Environment Setup")
    print("=" * 50)
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Working directory: {os.getcwd()}")
    print()
    
    # Step 1: Check Mamba installation
    if not check_mamba_installation():
        sys.exit(1)
    
    # Step 2: Check NVIDIA driver (optional but recommended)
    nvidia_available = check_nvidia_driver()
    if not nvidia_available:
        print("⚠️  Continuing without GPU support...")
    
    # Step 3: Create environment
    if not create_environment():
        sys.exit(1)
    
    # Step 4: Test environment
    if not test_environment():
        print("⚠️  Environment test failed, but continuing...")
    
    # Step 5: GPU verification (if NVIDIA available)
    if nvidia_available:
        if not run_gpu_verification():
            print("⚠️  GPU verification failed, but environment is ready for CPU usage")
    
    # Step 6: Print next steps
    print_next_steps()

if __name__ == "__main__":
    main()
