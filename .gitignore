# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/
venv
# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

.env
*.h5

*.pth

# Ignore coverage files
.coverage
coverage.txt
coverage.xml
/test_scaler.pkl
/dummy_model_path.pkl
dota-2-pro-league-matches-2023-metadata.json
/dataset/202507
/dataset/kaggle_data
.clinerules/about.md
dataset/train_data