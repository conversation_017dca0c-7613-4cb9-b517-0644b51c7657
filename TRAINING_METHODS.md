## Method 1: Kaggle Dataset (Recommended)

**Advantages**: No API limits, faster, more reliable, enhanced features, GPU acceleration

```bash
# Activate Mamba environment with GPU support
mamba activate dota2predictor-env

# Verify GPU functionality
python verify_gpu.py

# Step 1: Validate data quality
python dataset/validate_training_data.py

# Step 2: Process Kaggle dataset with player-hero win rates
python dataset/create_training_data_from_kaggle.py

# Step 3: Train model with GPU acceleration and full 370-feature set
python ml/create_model_match_predict.py
```

## Method 2: Incremental Learning (Automatic)

**How it works**: Automatically retrains with new prediction results

```python
# Triggered automatically in start.py message handler
main_ml = MainML(None, "xgb_model.pkl")
main_ml.load_model()
main_ml.incremental_train_with_new_data(incremental_learning_batch)
```

**Configuration** in `config.py`:
```python
incremental_learning_batch = 50  # Retrain after 50 new results
```

## Method 3: Full Manual Retraining (Legacy)

**Use case**: Complete model overhaul with new datasets

```bash
# Activate Mamba environment
mamba activate dota2predictor-env

# Step 1: Generate new training data (takes hours)
python dataset/generate_dataset_match_predict.py

# Step 2: Manually combine tournament CSV files
# Merge all premium_league_matches_*.csv into all_data_match_predict.csv

# Step 3: Train new model with GPU acceleration
python ml/create_model_match_predict.py
```