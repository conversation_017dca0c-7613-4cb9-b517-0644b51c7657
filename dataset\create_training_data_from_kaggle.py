import pandas as pd
import numpy as np
from tqdm import tqdm
import os
import sys

# Add the project root to path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def load_kaggle_data(base_dir="dataset/kaggle_data", start_year=2023):
    """Load and combine CSV files from multiple year/month directories."""
    print(f"Loading Kaggle dataset files from {start_year} onwards...")
    
    all_metadata = []
    all_players = []
    all_picks_bans = []
    
    # Get all subdirectories from the base directory
    try:
        subdirectories = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
    except FileNotFoundError:
        print(f"Error: Base directory '{base_dir}' not found.")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

    for subdir in subdirectories:
        # Extract year from directory name (e.g., '2023', '202501')
        try:
            year = int(subdir[:4])
        except ValueError:
            print(f"Skipping directory with non-year name: {subdir}")
            continue

        if year >= start_year:
            print(f"Processing directory: {subdir}")
            data_dir = os.path.join(base_dir, subdir)
            
            # Define file paths
            metadata_path = os.path.join(data_dir, "main_metadata.csv")
            players_path = os.path.join(data_dir, "players.csv")
            picks_bans_path = os.path.join(data_dir, "picks_bans.csv")
            
            # Check if all required files exist
            if not all(os.path.exists(p) for p in [metadata_path, players_path, picks_bans_path]):
                print(f"Warning: Skipping directory {subdir} due to missing required CSV files.")
                continue
            
            # Load data
            try:
                main_metadata = pd.read_csv(metadata_path)
                players = pd.read_csv(players_path)
                picks_bans = pd.read_csv(picks_bans_path)
                
                all_metadata.append(main_metadata)
                all_players.append(players)
                all_picks_bans.append(picks_bans)
            except Exception as e:
                print(f"Error loading data from {subdir}: {e}")

    if not all_metadata:
        print("No data loaded. Please check the directory structure and file names.")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

    # Concatenate all dataframes
    combined_metadata = pd.concat(all_metadata, ignore_index=True)
    combined_players = pd.concat(all_players, ignore_index=True)
    combined_picks_bans = pd.concat(all_picks_bans, ignore_index=True)
    
    print("\nFinished loading and combining data:")
    print(f"Total matches loaded: {len(combined_metadata)}")
    print(f"Total player records loaded: {len(combined_players)}")
    print(f"Total picks/bans loaded: {len(combined_picks_bans)}")
    
    return combined_metadata, combined_players, combined_picks_bans

def calculate_player_hero_winrates(main_metadata, players, picks_bans):
    """Calculate win rates for each player-hero combination from the dataset"""
    print("Calculating player-hero win rates...")
    
    # Filter for picks only
    picks_only = picks_bans[picks_bans['is_pick'] == True].copy()
    
    # Merge picks with players to get account_id for each pick
    # We need to match based on match_id, hero_id, and team
    picks_with_players = picks_only.merge(
        players[['match_id', 'hero_id', 'account_id', 'team_number']],
        on=['match_id', 'hero_id'],
        how='left'
    )
    
    # Filter out rows where we couldn't match (account_id is NaN)
    picks_with_players = picks_with_players[picks_with_players['account_id'].notna()]
    
    # Merge with match outcomes
    picks_with_outcomes = picks_with_players.merge(
        main_metadata[['match_id', 'radiant_win']],
        on='match_id',
        how='left'
    )
    
    # Calculate if each player won their match
    # team_number: 0 = Radiant, 1 = Dire
    # radiant_win: True if Radiant won, False if Dire won
    picks_with_outcomes['player_won'] = (
        (picks_with_outcomes['team_number'] == 0) & (picks_with_outcomes['radiant_win'] == True)
    ) | (
        (picks_with_outcomes['team_number'] == 1) & (picks_with_outcomes['radiant_win'] == False)
    )
    
    # Group by player and hero to calculate win rates
    player_hero_stats = picks_with_outcomes.groupby(['account_id', 'hero_id']).agg({
        'player_won': ['count', 'sum']
    }).reset_index()
    
    # Flatten column names
    player_hero_stats.columns = ['account_id', 'hero_id', 'games_played', 'games_won']
    
    # Calculate win rate
    player_hero_stats['player_hero_winrate'] = (
        player_hero_stats['games_won'] / player_hero_stats['games_played']
    )
    
    # For players with only 1 game on a hero, use a default win rate of 0.5
    player_hero_stats.loc[player_hero_stats['games_played'] == 1, 'player_hero_winrate'] = 0.5
    
    print(f"Calculated win rates for {len(player_hero_stats)} player-hero combinations")
    
    return player_hero_stats[['account_id', 'hero_id', 'player_hero_winrate']]

def calculate_advanced_team_stats(team_data, stat_name):
    """Calculate advanced statistical features for a team statistic"""
    values = team_data[stat_name].values
    
    # Handle edge cases
    if len(values) == 0:
        return {f'{stat_name}_avg': 0, f'{stat_name}_std': 0, f'{stat_name}_min': 0, 
                f'{stat_name}_max': 0, f'{stat_name}_range': 0, f'{stat_name}_skewness': 0, 
                f'{stat_name}_confidence': 0}
    
    avg_val = np.mean(values)
    std_val = np.std(values)
    min_val = np.min(values)
    max_val = np.max(values)
    range_val = max_val - min_val
    
    # Calculate skewness (measure of asymmetry)
    if std_val > 0:
        skewness = np.mean(((values - avg_val) / std_val) ** 3)
        confidence = 1 / (std_val + 1e-6)  # Add small epsilon to avoid division by zero
    else:
        skewness = 0
        confidence = 1
    
    return {
        f'{stat_name}_avg': avg_val,
        f'{stat_name}_std': std_val,
        f'{stat_name}_min': min_val,
        f'{stat_name}_max': max_val,
        f'{stat_name}_range': range_val,
        f'{stat_name}_skewness': skewness,
        f'{stat_name}_confidence': confidence
    }

def create_match_features(main_metadata, players, player_hero_winrates):
    """Create the final training dataset with enhanced statistical features"""
    print("Creating match-level features with advanced statistical aggregations...")
    
    # Start with match metadata
    matches = main_metadata[['match_id', 'radiant_win']].copy()
    
    # Get player data for each match
    player_data = players[[
        'match_id', 'account_id', 'hero_id', 'team_number', 'team_slot',
        'kills', 'deaths', 'assists', 'gold_per_min', 'xp_per_min',
        'teamfight_participation', 'obs_placed', 'sen_placed', 'net_worth',
        'roshans_killed', 'last_hits', 'denies', 'level', 'hero_damage',
        'tower_damage'
    ]].copy()
    
    # Merge with player-hero win rates
    player_data = player_data.merge(
        player_hero_winrates,
        on=['account_id', 'hero_id'],
        how='left'
    )
    
    # Fill missing win rates with 0.5 (default for new player-hero combinations)
    player_data['player_hero_winrate'] = player_data['player_hero_winrate'].fillna(0.5)
    
    # Calculate KDA ratio
    player_data['kda'] = (player_data['kills'] + player_data['assists']) / (player_data['deaths'] + 1)
    
    # Sort by match_id and team_slot to ensure consistent ordering
    player_data = player_data.sort_values(['match_id', 'team_number', 'team_slot'])
    
    # Define statistics to calculate advanced aggregations for
    advanced_stats = [
        'kills', 'deaths', 'assists', 'gold_per_min', 'xp_per_min',
        'net_worth', 'last_hits', 'denies', 'level', 'hero_damage',
        'tower_damage', 'kda', 'player_hero_winrate'
    ]
    
    # Define simple sum/count statistics
    sum_stats = ['obs_placed', 'sen_placed', 'roshans_killed', 'teamfight_participation']
    
    final_dataset = []
    
    for match_id in tqdm(matches['match_id'].unique(), desc="Processing matches"):
        match_players = player_data[player_data['match_id'] == match_id]
        
        # Skip matches that don't have exactly 10 players
        if len(match_players) != 10:
            continue
            
        # Separate radiant and dire players
        radiant_players = match_players[match_players['team_number'] == 0].sort_values('team_slot')
        dire_players = match_players[match_players['team_number'] == 1].sort_values('team_slot')
        
        # Skip if we don't have exactly 5 players per team
        if len(radiant_players) != 5 or len(dire_players) != 5:
            continue
            
        # Create row for this match
        row = {'match_id': match_id}
        
        # Add individual player features (keeping existing structure for compatibility)
        feature_columns = [
            'hero_id', 'kills', 'deaths', 'assists', 'gold_per_min', 'xp_per_min',
            'teamfight_participation', 'obs_placed', 'sen_placed', 'net_worth',
            'roshans_killed', 'last_hits', 'denies', 'level', 'hero_damage',
            'tower_damage', 'player_hero_winrate'
        ]
        
        # Fill missing numerical data with 0
        for col in feature_columns:
            if col != 'hero_id' and col != 'player_hero_winrate':
                player_data[col] = player_data[col].fillna(0)

        player_data['kda'] = (player_data['kills'] + player_data['assists']) / (player_data['deaths'] + 1)
        
        # Add radiant player features
        for i, (_, player) in enumerate(radiant_players.iterrows()):
            for feature in feature_columns:
                row[f'radiant_player_{i+1}_{feature}'] = player[feature]
        
        # Add dire player features  
        for i, (_, player) in enumerate(dire_players.iterrows()):
            for feature in feature_columns:
                row[f'dire_player_{i+1}_{feature}'] = player[feature]
        
        # Add advanced team-level statistical aggregations
        for team_name, team_data in [('radiant', radiant_players), ('dire', dire_players)]:
            # Calculate advanced statistics for key metrics
            for stat in advanced_stats:
                if stat in team_data.columns:
                    advanced_features = calculate_advanced_team_stats(team_data, stat)
                    for feature_name, value in advanced_features.items():
                        row[f'{team_name}_team_{feature_name}'] = value
            
            # Calculate simple aggregations for sum/count statistics
            for stat in sum_stats:
                if stat in team_data.columns:
                    row[f'{team_name}_team_{stat}_sum'] = team_data[stat].sum()
                    row[f'{team_name}_team_{stat}_avg'] = team_data[stat].mean()
        
        # Add match outcome
        match_outcome = matches[matches['match_id'] == match_id]['radiant_win'].iloc[0]
        row['radiant_win'] = match_outcome
        
        final_dataset.append(row)
    
    final_df = pd.DataFrame(final_dataset)
    print(f"Created enhanced dataset with {len(final_df)} matches and {len(final_df.columns)} features")
    
    return final_df

def main():
    """Main function to create the training dataset"""
    print("Starting Kaggle dataset processing...")
    
    # Load data
    main_metadata, players, picks_bans = load_kaggle_data()
    
    # Calculate player-hero win rates
    player_hero_winrates = calculate_player_hero_winrates(main_metadata, players, picks_bans)
    
    # Create final dataset
    final_dataset = create_match_features(main_metadata, players, player_hero_winrates)
    
    # Save the dataset
    output_path = "dataset/train_data/all_data_match_predict.csv"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    final_dataset.to_csv(output_path, index=False)
    
    print(f"Dataset saved to {output_path}")
    print(f"Final dataset shape: {final_dataset.shape}")
    
    # Show some basic statistics
    print("\nDataset statistics:")
    print(f"Total matches: {len(final_dataset)}")
    print(f"Radiant wins: {final_dataset['radiant_win'].sum()} ({final_dataset['radiant_win'].mean():.2%})")
    print(f"Dire wins: {(~final_dataset['radiant_win']).sum()} ({(~final_dataset['radiant_win']).mean():.2%})")
    
    # Show sample of features
    print("\nSample features (first 5 columns):")
    print(final_dataset.iloc[:5, :5])

if __name__ == "__main__":
    main()
