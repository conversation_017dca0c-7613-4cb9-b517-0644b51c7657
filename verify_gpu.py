#!/usr/bin/env python3
"""
GPU Verification Script for Dota 2 Predictor
Tests XGBoost CUDA functionality with RTX 40-series cards
"""

import sys
import numpy as np

def check_xgboost_gpu():
    """Test XGBoost GPU functionality"""
    try:
        import xgboost as xgb
        print(f"✅ XGBoost version: {xgb.__version__}")
        
        # Create dummy training data
        X = np.random.rand(1000, 50)  # Simulate match features
        y = np.random.randint(0, 2, 1000)  # Binary classification (win/loss)
        dtrain = xgb.DMatrix(X, label=y)
        
        # XGBoost parameters for GPU training
        params = {
            'objective': 'binary:logistic',
            'eval_metric': 'logloss',
            'device': 'cuda',
            'tree_method': 'hist',  # GPU-accelerated histogram method
            'max_depth': 6,
            'learning_rate': 0.1
        }
        
        print("\n🚀 Testing GPU training...")
        bst = xgb.train(params, dtrain, num_boost_round=50, verbose_eval=False)
        
        # Test prediction
        predictions = bst.predict(dtrain)
        accuracy = np.mean((predictions > 0.5) == y)
        
        print(f"✅ SUCCESS! XGBoost GPU training completed")
        print(f"📊 Dummy model accuracy: {accuracy:.2%}")
        print(f"🎮 Your RTX 4070 is ready for Dota 2 match prediction!")
        
        return True
        
    except ImportError:
        print("❌ XGBoost not installed")
        return False
    except Exception as e:
        print(f"❌ GPU training failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure NVIDIA drivers are installed")
        print("2. Check CUDA toolkit installation: mamba list cudatoolkit")
        print("3. Verify cuDNN: mamba list cudnn")
        return False

def check_cuda_availability():
    """Check CUDA availability and system info"""
    try:
        print(f"🔍 Checking CUDA devices...")

        # Check nvidia-smi
        import subprocess
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected:")
            lines = result.stdout.split('\n')

            # Extract GPU info
            for line in lines:
                if 'RTX' in line or 'GTX' in line or 'GeForce' in line:
                    # Clean up the line to show relevant GPU info
                    parts = line.split()
                    if len(parts) > 3:
                        gpu_info = ' '.join(parts[1:4])  # Get GPU name
                        print(f"   GPU: {gpu_info}")

            # Extract driver version
            for line in lines:
                if 'Driver Version:' in line:
                    driver_match = line.split('Driver Version:')[1].split()[0]
                    print(f"   Driver Version: {driver_match}")
                    break

            # Extract CUDA version
            for line in lines:
                if 'CUDA Version:' in line:
                    cuda_match = line.split('CUDA Version:')[1].split()[0]
                    print(f"   CUDA Version: {cuda_match}")
                    break
        else:
            print("⚠️  nvidia-smi not available")
            return False

        # Check CUDA toolkit installation
        try:
            import subprocess
            nvcc_result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
            if nvcc_result.returncode == 0:
                for line in nvcc_result.stdout.split('\n'):
                    if 'release' in line.lower():
                        print(f"   NVCC: {line.strip()}")
                        break
            else:
                print("   NVCC: Not found (using conda CUDA toolkit)")
        except:
            print("   NVCC: Not found (using conda CUDA toolkit)")

        return True

    except Exception as e:
        print(f"⚠️  Could not check CUDA devices: {e}")
        return False

def check_environment_packages():
    """Check critical package installations"""
    print("📦 Checking package installations...")

    packages = {
        'numpy': 'NumPy for numerical computing',
        'pandas': 'Pandas for data manipulation',
        'sklearn': 'Scikit-learn for ML utilities',
        'xgboost': 'XGBoost for gradient boosting',
        'requests': 'HTTP requests library',
        'sqlalchemy': 'Database ORM'
    }

    failed_packages = []

    for package, description in packages.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {package} {version}: {description}")
        except ImportError:
            print(f"❌ {package}: MISSING - {description}")
            failed_packages.append(package)

    return len(failed_packages) == 0

def main():
    """Main verification function"""
    print("🎯 Dota 2 Predictor - GPU Environment Verification")
    print("=" * 50)

    # Check package installations
    packages_ok = check_environment_packages()
    print()

    # Check CUDA
    cuda_available = check_cuda_availability()
    print()

    # Test XGBoost GPU
    gpu_success = check_xgboost_gpu()

    print("\n" + "=" * 50)

    if packages_ok and gpu_success:
        print("🎉 Environment setup complete! Ready for GPU-accelerated ML training.")
        print("\n📝 Next steps:")
        print("1. Start development: jupyter lab")
        print("2. Train models: python ml/create_model_match_predict.py")
        print("3. Start bot: python start.py")
        print("\n💡 Performance tips:")
        print("- Monitor GPU usage: nvidia-smi")
        print("- Adjust batch sizes based on VRAM usage")
        print("- Use 'device=cuda' in XGBoost parameters")
    elif packages_ok and not gpu_success:
        print("⚠️  Environment ready for CPU training (GPU acceleration unavailable)")
        print("\n📝 Next steps:")
        print("1. Train models with CPU: python ml/create_model_match_predict.py")
        print("2. Start bot: python start.py")
        print("\n🔧 To enable GPU:")
        print("- Check NVIDIA driver installation")
        print("- Verify CUDA toolkit: mamba list cuda")
        print("- Reinstall XGBoost: mamba install xgboost -c conda-forge")
    else:
        print("❌ Environment setup incomplete. Please fix package issues.")
        if not packages_ok:
            print("\n🔧 Fix package issues:")
            print("- Recreate environment: mamba env create -f environment.yml")
            print("- Check environment: mamba list")
        sys.exit(1)

if __name__ == "__main__":
    main()