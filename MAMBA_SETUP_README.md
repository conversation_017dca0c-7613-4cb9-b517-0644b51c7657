# Dota 2 Predictor - Mamba Environment Setup Guide

This guide will help you set up a robust development environment for the Dota 2 Predictor project using Mamba/Miniforge with GPU support for XGBoost training.

## 🎯 Why Mamba?

- **Superior Dependency Management**: Handles complex ML dependencies including CUDA, cuDNN, and system libraries
- **Speed**: 10x faster than conda for environment creation and package installation
- **GPU Support**: Automatic detection and installation of GPU-enabled packages
- **Reproducibility**: Exact environment replication across machines
- **Isolation**: No conflicts between different projects

## 📋 Prerequisites

### 1. NVIDIA Driver (For GPU Support)
- **Required**: Latest NVIDIA Game Ready or Studio Driver for RTX 4070
- **Download**: [NVIDIA Driver Downloads](https://www.nvidia.com/download/index.aspx)
- **Verification**: Run `nvidia-smi` in Command Prompt

### 2. Miniforge Installation
1. **Download**: [Miniforge3-Windows-x86_64.exe](https://github.com/conda-forge/miniforge#miniforge3)
2. **Install**: 
   - Choose "Install for me only"
   - Accept default installation path
   - **DO NOT** add to PATH (use Miniforge Prompt instead)

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)
```bash
# Open Miniforge Prompt from Start Menu
cd path\to\dota2predictor2
python setup_mamba_environment.py
```

### Option 2: Manual Setup
```bash
# 1. Create environment
mamba env create -f environment.yml

# 2. Activate environment
mamba activate dota2predictor-env

# 3. Verify GPU functionality
python verify_gpu.py
```

## 📦 Environment Details

### Core Components
- **Python**: 3.11 (optimal for ML libraries)
- **CUDA Toolkit**: 12.4 (RTX 40-series compatible)
- **cuDNN**: Latest compatible version
- **XGBoost**: 2.1+ with GPU acceleration

### Key Packages
```yaml
# Data Science & ML
- numpy=1.26.4
- pandas=2.2.2
- scikit-learn=1.5.1
- xgboost>=2.1.0 (GPU-enabled)
- matplotlib=3.9.1

# Web Scraping & HTTP
- beautifulsoup4=4.12.3
- requests=2.32.3
- lxml=5.2.2

# Database
- sqlalchemy=2.0.31
- psycopg2 (conda version)

# Development
- jupyterlab
- pre-commit=3.8.0
- coverage=7.6.0
```

## 🔧 Daily Workflow

### Environment Activation
```bash
# Always start with this
mamba activate dota2predictor-env
```

### Package Management
```bash
# Update all packages
mamba update --all

# Install new package
mamba install package_name

# Install pip-only package
pip install package_name

# List installed packages
mamba list
```

### Development Commands
```bash
# Start Jupyter for ML experimentation
jupyter lab

# Run GPU verification
python verify_gpu.py

# Start the bot
python start.py

# Run tests
python -m pytest tests/
```

## 🎮 GPU Verification

The `verify_gpu.py` script tests XGBoost CUDA functionality:

```python
# Expected output for successful GPU setup
✅ XGBoost version: 2.1.0
🚀 Testing GPU training...
✅ SUCCESS! XGBoost GPU training completed
📊 Dummy model accuracy: 52%
🎮 Your RTX 4070 is ready for Dota 2 match prediction!
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. "mamba: command not found"
**Solution**: Use Miniforge Prompt, not regular Command Prompt

#### 2. GPU not detected
```bash
# Check NVIDIA driver
nvidia-smi

# Check CUDA installation
mamba list cuda

# Reinstall CUDA toolkit
mamba install cuda-toolkit=12.4 -c conda-forge
```

#### 3. XGBoost GPU training fails
```bash
# Check XGBoost installation
python -c "import xgboost as xgb; print(xgb.__version__)"

# Reinstall XGBoost
mamba uninstall xgboost
mamba install xgboost -c conda-forge
```

#### 4. Environment creation fails
```bash
# Clean Mamba cache
mamba clean --all

# Remove corrupted environment
mamba env remove -n dota2predictor-env

# Recreate environment
mamba env create -f environment.yml
```

### Diagnostic Commands
```bash
# Environment information
mamba info

# Check for conflicts
mamba list --revisions

# Export environment for backup
mamba env export > environment_backup.yml
```

## 🔄 Environment Updates

### Updating Dependencies
```bash
# Update environment.yml with new packages
# Then update existing environment
mamba env update -f environment.yml --prune
```

### Version Pinning
```yaml
# Pin specific versions in environment.yml
dependencies:
  - xgboost=2.1.0  # Exact version
  - numpy>=1.26    # Minimum version
  - pandas~=2.2.0  # Compatible release
```

## 🐳 Docker Integration

For production deployment:
```dockerfile
FROM mambaorg/micromamba:1.5.1
COPY environment.yml /tmp/
RUN micromamba install -y -n base -f /tmp/environment.yml
```

## 📊 Performance Optimization

### GPU Memory Management
```python
# XGBoost GPU parameters for RTX 4070 (12GB VRAM)
params = {
    'device': 'cuda',
    'tree_method': 'hist',
    'gpu_hist_max_bins': 256,  # Adjust based on VRAM
    'max_depth': 8,
    'learning_rate': 0.1
}
```

### Batch Size Recommendations
- **RTX 4070 (12GB)**: Batch size 1000-5000 for training
- **Monitor VRAM**: Use `nvidia-smi` during training

## 🔗 Useful Links

- [Miniforge GitHub](https://github.com/conda-forge/miniforge)
- [XGBoost GPU Documentation](https://xgboost.readthedocs.io/en/stable/gpu/)
- [NVIDIA CUDA Downloads](https://developer.nvidia.com/cuda-downloads)
- [Conda-Forge Channel](https://conda-forge.org/)

## 📞 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Run `python setup_mamba_environment.py` for automated diagnosis
3. Verify GPU setup with `python verify_gpu.py`
4. Check environment with `mamba list` and `mamba info`

---

**Ready to start developing!** 🚀

Your Mamba environment provides a robust, reproducible, and GPU-accelerated setup for machine learning development with the Dota 2 Predictor project.
