name: Build Image on Tag Push

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+'

jobs:
  create_docker_image:
    name: Build and push docker image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract version from version.py
        id: get_version
        run: |
          version=$(grep '__version__' version.py | cut -d '"' -f 2)
          echo "App Version: $version"
          echo "::set-output name=VERSION::$version"

      - name: Check if version matches the Git tag
        run: |
          version=${{ steps.get_version.outputs.VERSION }}
          tag=${{ github.ref_name }}

          # Compare the extracted version and tag (both should match)
          if [ "$version" != "$tag" ]; then
            echo "Error: Version ($version) does not match Git tag ($tag)."
            exit 1  # Fail the workflow if they don't match
          else
            echo "Success: Version ($version) matches Git tag ($tag)."
          fi

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and tag Docker image
        run: |
          docker build -t masterhood13/dota2predictor:${{ github.ref_name }} .
          docker tag masterhood13/dota2predictor:${{ github.ref_name }} masterhood13/dota2predictor:latest

      - name: Push Docker image to Docker Hub
        run: |
          docker push masterhood13/dota2predictor:${{ github.ref_name }}
          docker push masterhood13/dota2predictor:latest
