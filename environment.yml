# Mamba/Conda environment for Dota 2 Predictor ML project
name: dota2predictor-env

channels:
  - conda-forge
  - nodefaults

dependencies:
  # --- Core & GPU ---
  - python=3.11
  - pip
  - cuda-toolkit=12.4   # Updated CUDA toolkit for RTX 40-series (better compatibility)
  - cudnn               # cuDNN for deep learning acceleration
  - xgboost>=2.1.0      # GPU-enabled XGBoost

  # --- Data Science & ML ---
  - numpy=1.26.4
  - pandas=2.2.2
  - scikit-learn=1.5.1
  - scipy=1.14.0
  - matplotlib=3.9.1
  - pillow=10.4.0
  - joblib=1.4.2
  - pandera=0.20.1

  # --- Web Scraping & HTTP ---
  - beautifulsoup4=4.12.3
  - lxml=5.2.2
  - requests=2.32.3
  - urllib3=2.2.2

  # --- Database ---
  - sqlalchemy=2.0.31
  - psycopg2             # Use conda version instead of psycopg2-binary

  # --- Utilities ---
  - python-dotenv=1.0.1
  - tqdm=4.67.1
  - click=8.1.7
  - python-dateutil=2.9.0

  # --- Development & Testing ---
  - pre-commit=3.8.0
  - coverage=7.6.0
  - jupyterlab           # For ML experimentation
  - pytest               # Better testing framework

  # --- Pip-only packages ---
  - pip:
    - pyTelegramBotAPI==4.14.0    # Telegram bot framework
    - requests-toolbelt==1.0.0    # HTTP utilities
    - cloudscraper==1.2.71        # Cloudflare bypass
    - pathspec==0.12.1            # Path matching utilities
    - platformdirs==4.2.2         # Platform directories
    - threadpoolctl==3.5.0        # Thread pool control
    - typing_extensions==4.12.2   # Type hints backport
    - tomli==2.0.1               # TOML parser